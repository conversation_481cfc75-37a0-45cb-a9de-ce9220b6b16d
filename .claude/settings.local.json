{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(claude mcp remove fetch memory time)", "<PERSON><PERSON>(claude mcp list)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "<PERSON><PERSON>(git clone:*)", "<PERSON><PERSON>(mv:*)", "Bash(npx:*)", "<PERSON><PERSON>(mcp call:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/Users/<USER>/mcp/scripts/test-mcp-server.sh:*)", "Bash(npm info:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(node:*)"], "deny": []}, "enableAllProjectMcpServers": false}