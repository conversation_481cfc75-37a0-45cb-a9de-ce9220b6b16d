/* ALIAS Design System Token Overrides for Geist */
/* Keep Geist's variable contract; override only neutral greys for deep-black aesthetic */

:root {
  /* ALIAS Black & White Base */
  --geist-background: #ffffff;
  --geist-foreground: #000000;

  /* ALIAS Neutral Greys - Light Mode */
  --geist-accents-1: #fafafa;
  --geist-accents-2: #eaeaea;
  --geist-accents-3: #bfbfbf;
  --geist-accents-4: #999999;
  --geist-accents-5: #666666;
  --geist-accents-6: #444444;
  --geist-accents-7: #333333;
  --geist-accents-8: #111111;

  /* Keep all Geist semantic colors untouched */
  /* --geist-success, --geist-error, --geist-warning, etc. remain as-is */
}

[data-theme="dark"] {
  /* ALIAS Black & White Base - Dark Mode */
  --geist-background: #000000;
  --geist-foreground: #ffffff;

  /* ALIAS Neutral Greys - Dark Mode */
  --geist-accents-1: #111111;
  --geist-accents-2: #1a1a1a;
  --geist-accents-3: #333333;
  --geist-accents-4: #444444;
  --geist-accents-5: #666666;
  --geist-accents-6: #808080;
  --geist-accents-7: #bfbfbf;
  --geist-accents-8: #eaeaea;

  /* Keep all Geist semantic colors untouched */
}

/* ALIAS-specific utility classes */
.alias-text-gradient {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.alias-card-hover {
  transition: all 0.2s ease;
}

.alias-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Remove any orange branding animations */
.no-orange-glow {
  box-shadow: none !important;
  animation: none !important;
}
