@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fade up animation */
@keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-up {
  animation: fade-up 0.5s ease-out forwards;
}

/* Glowing border animation for email column */
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(251, 146, 60, 0.5),
      0 0 40px rgba(251, 146, 60, 0.3),
      0 0 60px rgba(251, 146, 60, 0.1);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(251, 146, 60, 0.8),
      0 0 60px rgba(251, 146, 60, 0.5),
      0 0 90px rgba(251, 146, 60, 0.3);
  }
}

.email-column-glow {
  animation: glow-pulse 2s ease-in-out infinite;
  position: relative;
}

/* Rounded corners for email column */
.email-column-rounded-top {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.email-column-rounded-bottom {
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.email-column-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(45deg, rgba(251, 146, 60, 0.8), rgba(251, 191, 36, 0.8));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0.7;
  animation: glow-pulse 2s ease-in-out infinite;
}


@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 350 100% 62%;
    --primary-foreground: 0 0% 100%;
    --secondary: 240 5% 96%;
    --secondary-foreground: 222 47% 11%;
    --muted: 240 5% 96%;
    --muted-foreground: 240 4% 46%;
    --accent: 240 5% 96%;
    --accent-foreground: 222 47% 11%;
    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 6% 90%;
    --input: 240 6% 90%;
    --ring: 350 100% 62%;
    --radius: 0.75rem;
    
    /* New gradient variables */
    --gradient-primary: linear-gradient(135deg, #FF6B6B 0%, #FF5E5E 25%, #FF8E53 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-accent: linear-gradient(135deg, #FA8BFF 0%, #2BD2FF 52%, #2BFF88 90%);
    --gradient-subtle: linear-gradient(135deg, #FFECD2 0%, #FCB69F 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    
    /* Shadow variables */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-glow: 0 0 20px rgb(255 107 107 / 0.3);
    
    /* Animation durations and delays */
    --d-1: 150ms;
    --d-2: 300ms;
    --d-3: 500ms;
    --t-1: 200ms;
    --t-2: 400ms;
    --t-3: 600ms;
    --spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    --ease: cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dark {
    --background: 222 47% 7%;
    --foreground: 0 0% 98%;
    --card: 222 47% 9%;
    --card-foreground: 0 0% 98%;
    --popover: 222 47% 9%;
    --popover-foreground: 0 0% 98%;
    --primary: 350 100% 62%;
    --primary-foreground: 0 0% 100%;
    --secondary: 222 47% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 222 47% 15%;
    --muted-foreground: 215 20% 65%;
    --accent: 222 47% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 222 47% 20%;
    --input: 222 47% 20%;
    --ring: 350 100% 62%;
    
    /* Dark mode gradient adjustments */
    --gradient-primary: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-accent: linear-gradient(135deg, #FA8BFF 0%, #2BD2FF 52%, #2BFF88 90%);
    --gradient-subtle: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 142, 83, 0.1) 100%);
    --gradient-dark: linear-gradient(135deg, #0f0f1e 0%, #1a1a2e 100%);
    
    /* Dark mode shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.5);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.5), 0 2px 4px -2px rgb(0 0 0 / 0.5);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.5), 0 8px 10px -6px rgb(0 0 0 / 0.5);
    --shadow-glow: 0 0 30px rgb(255 107 107 / 0.5);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

:root {
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes text {
  to {
    background-position: 200% center;
  }
}

.animate-text {
  animation: text 5s ease infinite;
  background-size: 200% auto;
}

@keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-up {
  animation-name: fade-up;
  animation-fill-mode: forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.2s ease-out;
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 1000px 100%;
  animation: shimmer 2s infinite;
}

/* Slower pulse animation for loading cells */
@keyframes slow-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-slow-pulse {
  animation: slow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Very slow pulse animation for processing rows */
@keyframes processing-row-pulse {
  0%, 100% {
    opacity: 1;
    background-color: rgb(254 243 199 / 0.5); /* from-orange-50 */
  }
  50% {
    opacity: 0.85;
    background-color: rgb(254 249 195 / 0.7); /* to-yellow-50 */
  }
}

.animate-processing-row {
  animation: processing-row-pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background: linear-gradient(to right, rgb(254 243 199 / 0.5), rgb(254 249 195 / 0.5));
}

/* New animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgb(255 107 107 / 0.5),
                0 0 40px rgb(255 107 107 / 0.3);
  }
  50% { 
    box-shadow: 0 0 30px rgb(255 107 107 / 0.8),
                0 0 60px rgb(255 107 107 / 0.4);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes slide-up-fade {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes */
.glass {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-border {
  position: relative;
  background: linear-gradient(var(--background), var(--background)) padding-box,
              var(--gradient-primary) border-box;
  border: 2px solid transparent;
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: all 0.3s var(--spring);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Premium button styles */
.btn-gradient {
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s var(--ease);
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgb(255 107 107 / 0.3);
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-gradient:hover::before {
  left: 100%;
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s var(--spring);
  cursor: pointer;
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

/* Smooth number transitions */
.number-transition {
  transition: all 0.8s var(--spring);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}
