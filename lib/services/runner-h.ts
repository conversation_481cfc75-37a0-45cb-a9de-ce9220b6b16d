import axios from 'axios';

export interface RunnerHRunResponse {
  status: 'success' | 'error';
  result?: string;
  evidence?: string[]; // urls or screenshot links
  cost?: number; // cents
  error?: string;
}

/**
 * Thin wrapper around the Runner H REST API so the rest of the codebase can stay
 * provider-agnostic.
 *
 * The service is entirely optional – initialise only when
 * `RUNNER_H_API_KEY` is present.  When it is missing the caller should detect
 * the undefined instance and skip browser-navigation tasks.
 */
export class RunnerHService {
  private readonly apiKey: string;
  private readonly host: string;

  constructor({
    apiKey = process.env.RUNNER_H_API_KEY ?? '',
    host = process.env.RUNNER_H_HOST ?? 'https://api.runnerh.ai',
  } = {}) {
    this.apiKey = apiKey;
    this.host = host.replace(/\/$/, '');
  }

  /**
   * True if the service can be used.
   *
   * • Cloud mode → requires API key and default host.
   * • Local/self-host mode → assumes any non-default host is available, even
   *   without a key.
   */
  get isConfigured() {
    const defaultHost = 'https://api.runnerh.ai';
    return (
      // Cloud
      (this.host === defaultHost && Boolean(this.apiKey)) ||
      // Self-host (custom host implies user is running their own instance)
      this.host !== defaultHost
    );
  }

  /**
   * Runs an arbitrary browser task.
   * @param prompt Natural-language instruction for the web agent.
   */
  async runTask(prompt: string, timeoutMs = 180_000): Promise<RunnerHRunResponse> {
    if (!this.isConfigured) {
      return { status: 'error', error: 'Runner H not configured' };
    }

    try {
      const headers: Record<string, string> = {};
      if (this.apiKey) headers['Authorization'] = `Bearer ${this.apiKey}`;

      const { data } = await axios.post<RunnerHRunResponse>(
        `${this.host}/v1/agent/run`,
        { prompt },
        {
          headers,
          timeout: timeoutMs,
        },
      );
      return data;
    } catch (error) {
      console.error('[RunnerH] API error', error);
      return { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}
