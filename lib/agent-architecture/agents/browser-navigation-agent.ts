import type { EnrichmentField } from '@/lib/types';
import { RunnerHService } from '@/lib/services/runner-h';

export class BrowserNavigationAgent {
  constructor(private runner: RunnerHService) {}

  async execute(
    fields: EnrichmentField[],
    context: Record<string, unknown>,
    onAgentProgress?: (msg: string, type: 'info' | 'success' | 'warning' | 'agent') => void,
  ): Promise<Record<string, unknown>> {
    const results: Record<string, unknown> = {};

    for (const field of fields) {
      const prompt = this.buildPrompt(field, context);
      onAgentProgress?.(`Browser Agent executing: ${prompt.slice(0, 60)}…`, 'agent');

      const resp = await this.runner.runTask(prompt);

      if (resp.status === 'success') {
        results[field.name] = {
          value: resp.result ?? null,
          confidence: 0.75,
          sources: resp.evidence ?? [],
        };
        onAgentProgress?.(`Field "${field.name}" done`, 'success');
      } else {
        onAgentProgress?.(`Failed to enrich "${field.name}": ${resp.error ?? 'unknown'}`, 'warning');
      }
    }

    return results;
  }

  private buildPrompt(field: EnrichmentField, ctx: Record<string, unknown>): string {
    const companyName = (ctx['companyName'] as string | undefined) ||
      (ctx['emailContext'] as Record<string, unknown> | undefined)?.['companyNameGuess'] || '';
    const domain = (ctx['emailContext'] as Record<string, unknown> | undefined)?.['domain'] || '';

    return `For the company ${companyName || domain}, ${field.description || field.name}. Use the browser to find the answer and list any useful URLs as evidence.`;
  }
}
