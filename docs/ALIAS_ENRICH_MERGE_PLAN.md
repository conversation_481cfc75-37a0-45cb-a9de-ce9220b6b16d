# Alias Enrich – Internal Adoption & Merge Plan

This document captures the agreed-upon steps for ① re-branding the public *Fire Enrich* repo into our private **<PERSON><PERSON> Enrich** tool and ② eventually folding that codebase into the parent monorepo.

-------------------------------------------------------------------------------
## 1  One-Time Repo Re-Brand Checklist

1. **Repo / Package Identity**  
   • Rename GitHub repo → `alias-enrich` (private).  
   • Update `package.json` → `name`, `description`, `author`.

2. **App-wide Text & Logo**  
   • Global search-replace “Fire Enrich” → “<PERSON>as Enrich”.  
   • Update `<title>` & meta tags in `app/layout.tsx`.  
   • Replace logo files under `public/` and component import in `components/ui/logo.tsx`.

3. **Environment Flags / Unlimited Mode**  
   • Keep existing `FIRE_ENRICH_*` flags or rename to `ALIAS_ENRICH_*`.  
   • Edit checks in `app/fire-enrich/config.ts` as required.

4. **Routes & Folder Names**  
   • Optional: move `app/fire-enrich` → `app/alias-enrich`.  
   • Update path imports & landing-page links.

5. **Documentation**  
   • Rewrite `README.md` & `app/fire-enrich/README.md` headings.  
   • Remove/adjust deploy badge for internal use.

-------------------------------------------------------------------------------
## 2  Integrate Alias Design System

1. **Delivery Method** – Tailwind token file, React library, or CSS-in-JS.  
2. **Tailwind Tokens** – Extend `tailwind.config.ts` with Alias DS colours, fonts.  
3. **Component Migration** – Gradually swap/skin shadcn-ui components.  
4. **Global Styles** – Add DS typography & utility classes in `app/globals.css`.  
5. **Tooling** – Extend ESLint / Prettier configs if DS ships its own rules.

-------------------------------------------------------------------------------
## 3  Internal-Only Hardening

1. **Auth Gate** – Protect `/alias-enrich` route with SSO middleware.  
2. **Rate-Limit Config** – Relax or disable public limits in `lib/rate-limit.ts`.  
3. **Secrets Handling** – Move API keys to internal secret manager; inject via CI/CD.  
4. **Telemetry** – Wrap Firecrawl/OpenAI services with internal metrics.

-------------------------------------------------------------------------------
## 4  Regression Pass After Rename

1. `pnpm dev` smoke test – upload CSV → enrich → export.  
2. Check console for unresolved import paths.  
3. Validate logo, page title, favicon.  
4. Ensure Unlimited mode still triggers in dev.

-------------------------------------------------------------------------------
## 5  Future Field / Agent Changes

Rename does **not** affect agent logic.  Follow migration guide (identifier parser, agents, field router, UI presets) to expand beyond company e-mail enrichment.

-------------------------------------------------------------------------------
## 6  Merge into Parent Repository

### 6.1 Choose Strategy

A. **Monorepo (recommended)**  
   • `/apps/alias-enrich` – Next.js 15 UI.  
   • `/packages/alias-enrich-core` – agents/orchestrator (no UI).  
   • `/packages/alias-ui` – design system (if separate).

B. **Feature folder inside existing Next app** – quicker but less modular.

C. **Temporary git submodule** – keep decoupled until code stabilises.

### 6.2 Execution Steps (Monorepo Path)

1. Create branch `feat/alias-enrich-import` in parent.  
2. Copy folders:  
   • `app/`, `public/`, `next.config.ts` → `apps/alias-enrich/`  
   • `lib/`, `hooks/`, generic `components/` → `packages/alias-enrich-core/`  
3. Establish workspaces in root `package.json`; run `pnpm install`.  
4. Align dependency versions (React / Next / zod / openai).  
5. Add env vars (OPENAI_API_KEY, FIRECRAWL_API_KEY, ALIAS_ENRICH_UNLIMITED) to CI & hosting.  
6. Extend root ESLint / Prettier; hook into pre-commit.  
7. Configure build targets (`turbo.json` or similar).  
8. Local test: `pnpm turbo dev --filter=alias-enrich`.  
9. Push branch, open PR.

### 6.3 Component Sharing

• Import orchestrator anywhere in parent via:  
```ts
import { AgentOrchestrator } from "@alias/enrich-core";
```

### 6.4 Smoke-Test Matrix

1. Dev mode row enrichment.  
2. CI build & tests.  
3. Prod preview deploy.  
4. Verify unrelated parent routes remain unaffected.

-------------------------------------------------------------------------------
## 7  Status

*Document created ✧ ready for execution when the organisation schedules the re-brand & merge.*
